export default {
  messages(state: any) {
    return state.chat.messages;
  },
  backendUrl(state: any) {
    return state.config.backendUrl;
  },
  roleArn(state: any) {
    return state.config.roleArn;
  },
  callbackUrl(state: any) {
    return state.config.callbackUrl;
  },
  appRegion(state: any) {
    return state.config.appRegion;
  },
  appId(state: any) {
    return state.config.appId;
  },
  accesorId(state: any) {
    return state.config.accesorId;
  },
  ssoId(state: any) {
    return state.config.ssoId;
  },
  ssoRegion(state: any) {
    return state.config.ssoRegion;
  },
  qindexConfig(state: any) {
    return state;
  },
  qindexExpiration(state: any) {
    return state.expiration < Date.now();
  },
  isPLGMode(state: any) {
    return state.config.isPLGMode ?? true;
  },
  lastWizardStep(state: any) {
    return state.config.lastWizardStep;
  },
  /**
   * Determines if PLG toggle is available based on current context
   * Returns true if toggle should be available, false otherwise
   */
  isPLGToggleAvailable: (state: any, getters: any, rootState: any, rootGetters: any) => (route: any) => {
    // PLG toggle is only available for cluster creation mode
    // Check if current route matches the panel's location config
    if (!route) {
      return false;
    }

    const { query, params } = route;

    // Check if we're in create mode for provisioning.cattle.io.cluster resource
    const isCreateMode = query?.mode === 'create' || route.name?.includes('-create');
    const isClusterResource = params?.resource === 'provisioning.cattle.io.cluster' ||
                             route.name?.includes('cluster');

    return isCreateMode && isClusterResource;
  },
  /**
   * Gets the effective PLG mode considering toggle availability
   * If toggle is not available, defaults to advanced mode (false)
   */
  getEffectivePLGMode: (state: any, getters: any) => (route: any) => {
    const toggleAvailable = getters.isPLGToggleAvailable(route);

    if (!toggleAvailable) {
      // When toggle is not available (e.g., edit mode), default to advanced mode
      return false;
    }

    // When toggle is available, use the stored preference
    return getters.isPLGMode;
  }
};
