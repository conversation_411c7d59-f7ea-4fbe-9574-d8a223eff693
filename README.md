# Rancher SAAS Cloud UI Extension

A Product-Led Growth (PLG) focused Rancher extension that simplifies EKS cluster management by overriding default Rancher behavior and hiding non-essential features.

## Prerequisites

- Node.js ≥20
- [Task](https://taskfile.dev/) runner
- Git with submodules support

## Quick Start

Starting the development server will automatically initialize the dashboard submodule and symlink the extension into the dashboard runtime.

```bash
# Start development server
task dev
```

## Development Setup

This extension uses the Rancher Dashboard (v2.11.3) as a git submodule for TypeScript dependency resolution. The extension is symlinked into the dashboard runtime for development.

- **Extension code**: `pkg/rancher-saas/`
- **Dashboard submodule**: `dashboard/`
- **Available tasks**: Run `task --list` to see all available commands

## Testing

Jest tests require copying the extension directory instead of using symlinks due to module resolution issues:

```bash
# Remove symlink and copy extension for testing
rm dashboard/pkg/rancher-saas
cp -r pkg/rancher-saas dashboard/pkg/
cd dashboard && yarn test
# yarn jest pkg/rancher-saas/components/eks/__tests__/
```

## Architecture

The extension overrides existing EKS UI components and uses Rancher's plugin system to hide non-EKS functionality, creating a streamlined experience focused on Amazon EKS cluster provisioning and management.

